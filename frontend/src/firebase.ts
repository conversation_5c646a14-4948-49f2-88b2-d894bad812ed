import firebase from 'firebase/compat/app';
import 'firebase/compat/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_GOOGLE_API_KEY || "AIzaSyCePfnowJoDTFMg4tCj9x-Dd1XcFnKOy8s",
  authDomain: "tranquil-bison-465923-v9.firebaseapp.com",
  projectId: "tranquil-bison-465923-v9",
  storageBucket: "tranquil-bison-465923-v9.appspot.com",
  messagingSenderId: "546390650743",
  appId: "1:546390650743:web:your-app-id-here"
};

// Initialize Firebase
if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
}

const db = firebase.firestore();

// Connect to Firestore emulator in development (only if explicitly enabled)
if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_FIRESTORE_EMULATOR === 'true') {
  // Check if we're already connected to avoid reconnection errors
  try {
    db.useEmulator('localhost', 8080);
    console.log('Connected to Firestore emulator at localhost:8080');
  } catch (error) {
    // Emulator already connected, ignore error
    console.log('Firestore emulator already connected or not available');
  }
} else {
  console.log('Using production Firestore database');
}

export { db };
