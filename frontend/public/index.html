<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/gallery_tuner_light_mode.ico" id="favicon" />
    <link rel="icon" type="image/x-icon" href="%PUBLIC_URL%/gallery_tuner_light_mode.ico" id="favicon-x-icon" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/gallery_tuner_light_mode.ico" id="favicon-shortcut" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#667eea" />
    <meta
      name="description"
      content="Gallery Tuner - Compress and optimize your videos from Google Photos with ease. Professional video compression service with beautiful Material Design interface."
    />
    <meta name="keywords" content="video compression, Google Photos, video optimization, Gallery Tuner" />
    <meta name="author" content="Gallery Tuner" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/gallery_tuner_light_mode.png" id="apple-touch-icon" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Google Identity Services -->
    <script src="https://accounts.google.com/gsi/client" async></script>
    <title>Gallery Tuner</title>

    <!-- System theme-aware favicon script -->
    <script>
      (function() {
        function updateFavicon() {
          // Only check system theme, ignore app theme selection
          const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

          const iconSuffix = isDark ? 'dark_mode' : 'light_mode';
          const iconPath = `/gallery_tuner_${iconSuffix}.ico`;
          const pngPath = `/gallery_tuner_${iconSuffix}.png`;

          // Update all favicon links
          document.getElementById('favicon').href = iconPath;
          document.getElementById('favicon-x-icon').href = iconPath;
          document.getElementById('favicon-shortcut').href = iconPath;
          document.getElementById('apple-touch-icon').href = pngPath;
        }

        // Listen for system theme changes only
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateFavicon);

        // Initial update
        updateFavicon();
      })();
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <!-- Firebase SDK -->
    <script src="/__/firebase/9.17.2/firebase-app-compat.js"></script>
    <script src="/__/firebase/9.17.2/firebase-firestore-compat.js"></script>
    <script src="/__/firebase/init.js"></script>
  </body>
</html>
