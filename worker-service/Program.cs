using VidCompressor.Data;
using VidCompressor.Services;
using VidCompressor.WorkerService.Services;
using VidCompressor.Extensions;
using VidCompressor.Configuration;

var builder = WebApplication.CreateBuilder(args);

// Add Google Secret Manager as a configuration source
// This loads secrets directly at startup - no local files needed!
builder.Configuration.AddSecretManager(builder.Configuration);

// Set Firestore emulator environment variable early
var firestoreConfig = builder.Configuration.GetSection("Firestore");
if (firestoreConfig.GetValue<bool>("UseEmulator"))
{
    var emulatorHost = firestoreConfig.GetValue<string>("EmulatorHost");
    if (!string.IsNullOrEmpty(emulatorHost))
    {
        Environment.SetEnvironmentVariable("FIRESTORE_EMULATOR_HOST", emulatorHost);
        Console.WriteLine($"[WORKER-SERVICE] Set FIRESTORE_EMULATOR_HOST to: {emulatorHost}");
    }
}

// Add services to the container.
builder.Services.AddFirestore(builder.Configuration);

// Configure Google Cloud settings
builder.Services.Configure<GoogleCloudConfig>(builder.Configuration.GetSection("GoogleCloud"));

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
    });
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddHttpClient();

// Add shared services
builder.Services.AddScoped<GooglePhotosService>();
builder.Services.AddScoped<GoogleCloudStorageService>();
builder.Services.AddScoped<GoogleTranscoderService>();
builder.Services.AddScoped<ImageCompressionService>();
builder.Services.AddScoped<MediaMetadataService>();

// Add worker-specific services
builder.Services.AddScoped<CompressionBackgroundService>();
builder.Services.AddHostedService<CompressionBackgroundService>();
builder.Services.AddHostedService<BatchUploadService>();
builder.Services.AddHostedService<FileCleanupService>();

// Add SignalR client for notifications
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.MapControllers();

// Add a simple health check endpoint for Cloud Run startup probe
app.MapGet("/health", () => Results.Ok(new { status = "healthy", timestamp = DateTime.UtcNow }));

app.Run();
